<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role_id',
        'parent_id',
        'phone',
        'whatsapp',
        'telegram',
        'balance',
        'is_active',
        'settings',
        'last_login_at',
        'last_login_ip'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'settings' => 'array',
            'last_login_at' => 'datetime',
            'is_active' => 'boolean',
            'balance' => 'decimal:2'
        ];
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function parent()
    {
        return $this->belongsTo(User::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(User::class, 'parent_id');
    }

    public function commissions()
    {
        return $this->hasMany(Commission::class);
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function hasRole($role)
    {
        return $this->role->slug === $role;
    }

    public function canCreateUser($role = null)
    {
        $hierarchy = [
            'super-admin' => ['admin', 'dealer', 'retailer', 'agent', 'customer'],
            'admin' => ['dealer', 'retailer', 'agent', 'customer'],
            'dealer' => ['retailer', 'agent', 'customer'],
            'retailer' => ['agent', 'customer'],
            'agent' => ['customer'],
            'customer' => []
        ];

        // If no role is provided, return the array of allowed roles
        if ($role === null) {
            return $hierarchy[$this->role->slug] ?? [];
        }

        // If role is provided, check if it's allowed
        return in_array($role, $hierarchy[$this->role->slug] ?? []);
    }

    public function canEditUser($user)
    {
        // Users can always edit their own profile
        if ($this->id === $user->id) {
            return true;
        }

        // Check if the user was created by the current user
        if ($user->parent_id === $this->id) {
            return true;
        }

        // Super admin can edit any user
        if ($this->hasRole('super-admin')) {
            return true;
        }

        return false;
    }

    public function updateBalance($amount, $type = 'credit')
    {
        $this->balance = $type === 'credit' 
            ? $this->balance + $amount 
            : $this->balance - $amount;
        $this->save();
    }

    public function syncBalance()
    {
        $balance = $this->transactions()
            ->where('status', 'completed')
            ->sum(\DB::raw("CASE WHEN type = 'credit' THEN amount ELSE -amount END"));
        
        $this->balance = $balance;
        $this->save();
    }
}
